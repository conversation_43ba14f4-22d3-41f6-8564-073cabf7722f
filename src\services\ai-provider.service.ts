import { Injectable, Logger } from '@nestjs/common';
import { BedrockClaudeService, ClaudeMessage, BedrockClaudeConfig } from './bedrock-claude.service';
import Anthropic from '@anthropic-ai/sdk';

// Re-export ClaudeMessage for use in other services
export { ClaudeMessage } from './bedrock-claude.service';

export interface AIProviderConfig {
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stopSequences?: string[];
  model?: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    input_tokens: number;
    output_tokens: number;
  };
  provider: 'bedrock' | 'anthropic';
  model: string;
}

@Injectable()
export class AIProviderService {
  private readonly logger = new Logger(AIProviderService.name);
  private bedrockService: BedrockClaudeService;
  private anthropicClient: Anthropic;
  private primaryProvider: 'bedrock' | 'anthropic';
  private fallbackEnabled: boolean;

  constructor() {
    // Initialize services
    this.bedrockService = new BedrockClaudeService();
    this.anthropicClient = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });

    // Determine primary provider from environment
    this.primaryProvider = (process.env.AI_PROVIDER?.toLowerCase() === 'anthropic') ? 'anthropic' : 'bedrock';
    this.fallbackEnabled = process.env.AI_FALLBACK_ENABLED !== 'false'; // Default to true

    this.logger.log(`🤖 AI Provider initialized - Primary: ${this.primaryProvider}, Fallback: ${this.fallbackEnabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Send a message using the configured provider with fallback
   */
  async sendMessage(
    messages: ClaudeMessage[],
    config: AIProviderConfig = {}
  ): Promise<AIResponse> {
    // Try primary provider first
    try {
      if (this.primaryProvider === 'bedrock') {
        return await this.sendViaBedrock(messages, config);
      } else {
        return await this.sendViaAnthropic(messages, config);
      }
    } catch (primaryError) {
      this.logger.warn(`❌ Primary provider (${this.primaryProvider}) failed: ${primaryError.message}`);
      
      // Try fallback if enabled
      if (this.fallbackEnabled) {
        this.logger.log(`🔄 Attempting fallback to ${this.primaryProvider === 'bedrock' ? 'anthropic' : 'bedrock'}...`);
        
        try {
          if (this.primaryProvider === 'bedrock') {
            return await this.sendViaAnthropic(messages, config);
          } else {
            return await this.sendViaBedrock(messages, config);
          }
        } catch (fallbackError) {
          this.logger.error(`❌ Fallback provider also failed: ${fallbackError.message}`);
          throw new Error(`Both AI providers failed. Primary (${this.primaryProvider}): ${primaryError.message}. Fallback: ${fallbackError.message}`);
        }
      } else {
        throw primaryError;
      }
    }
  }

  /**
   * Simple completion method for backward compatibility
   */
  async complete(
    prompt: string,
    config: AIProviderConfig = {}
  ): Promise<string> {
    const messages: ClaudeMessage[] = [
      { role: 'user', content: prompt }
    ];

    const response = await this.sendMessage(messages, config);
    return response.content;
  }

  /**
   * Send message via AWS Bedrock
   */
  private async sendViaBedrock(
    messages: ClaudeMessage[],
    config: AIProviderConfig
  ): Promise<AIResponse> {
    const bedrockConfig: BedrockClaudeConfig = {
      maxTokens: config.maxTokens,
      temperature: config.temperature,
      topP: config.topP,
      stopSequences: config.stopSequences,
      model: config.model,
    };

    const response = await this.bedrockService.sendMessage(messages, bedrockConfig);
    
    return {
      content: response.content[0]?.text || '',
      usage: response.usage,
      provider: 'bedrock',
      model: config.model || process.env.BEDROCK_CLAUDE_MODEL || 'anthropic.claude-3-5-sonnet-20241022-v2:0'
    };
  }

  /**
   * Send message via Anthropic API
   */
  private async sendViaAnthropic(
    messages: ClaudeMessage[],
    config: AIProviderConfig
  ): Promise<AIResponse> {
    // Convert messages to Anthropic format
    const systemMessages = messages.filter(m => m.role === 'system');
    const conversationMessages = messages.filter(m => m.role !== 'system');

    const anthropicMessages = conversationMessages.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content
    }));

    const requestParams: any = {
      model: config.model || process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022',
      max_tokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.1,
      top_p: config.topP || 0.9,
      messages: anthropicMessages,
    };

    // Add system message if present
    if (systemMessages.length > 0) {
      requestParams.system = systemMessages.map(m => m.content).join('\n\n');
    }

    // Add stop sequences if provided
    if (config.stopSequences && config.stopSequences.length > 0) {
      requestParams.stop_sequences = config.stopSequences;
    }

    const response = await this.anthropicClient.messages.create(requestParams);

    return {
      content: response.content[0]?.type === 'text' ? response.content[0].text : '',
      usage: {
        input_tokens: response.usage.input_tokens,
        output_tokens: response.usage.output_tokens
      },
      provider: 'anthropic',
      model: requestParams.model
    };
  }

  /**
   * Health check for both providers
   */
  async healthCheck(): Promise<{
    bedrock: { status: string; model: string; region: string };
    anthropic: { status: string; model: string };
    primary: string;
    fallback: boolean;
  }> {
    const bedrockHealth = await this.bedrockService.healthCheck();
    
    let anthropicHealth = { status: 'unknown', model: 'N/A' };
    try {
      const testResponse = await this.anthropicClient.messages.create({
        model: process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022',
        max_tokens: 10,
        messages: [{ role: 'user', content: 'Hello' }]
      });
      anthropicHealth = {
        status: 'healthy',
        model: process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022'
      };
    } catch (error) {
      anthropicHealth = {
        status: 'unhealthy',
        model: process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022'
      };
    }

    return {
      bedrock: bedrockHealth,
      anthropic: anthropicHealth,
      primary: this.primaryProvider,
      fallback: this.fallbackEnabled
    };
  }

  /**
   * Get current provider configuration
   */
  getProviderConfig(): {
    primary: string;
    fallback: boolean;
    bedrockModel: string;
    anthropicModel: string;
  } {
    return {
      primary: this.primaryProvider,
      fallback: this.fallbackEnabled,
      bedrockModel: process.env.BEDROCK_CLAUDE_MODEL || 'anthropic.claude-3-5-sonnet-20241022-v2:0',
      anthropicModel: process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022'
    };
  }
}
