import { Injectable, Logger } from '@nestjs/common';
import { AIProviderService, ClaudeMessage, AIProviderConfig } from './ai-provider.service';

export interface AgentConfig {
  systemPrompt?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stopSequences?: string[];
  model?: string;
}

export interface AgentResponse {
  content: string;
  usage?: {
    input_tokens: number;
    output_tokens: number;
  };
  provider: 'bedrock' | 'anthropic';
  model: string;
  processingTime: number;
}

export interface Agent {
  process: (userPrompt: string, additionalConfig?: Partial<AgentConfig>) => Promise<AgentResponse>;
  processConversation: (messages: ClaudeMessage[], additionalConfig?: Partial<AgentConfig>) => Promise<AgentResponse>;
}

@Injectable()
export class AIAgentFactoryService {
  private readonly logger = new Logger(AIAgentFactoryService.name);
  private aiProvider: AIProviderService;

  constructor() {
    this.aiProvider = new AIProviderService();
    this.logger.log('🏭 AI Agent Factory initialized');
  }

  /**
   * Create a specialized agent for a specific task
   */
  createAgent(agentName: string, config: AgentConfig = {}): Agent {
    return {
      /**
       * Process a user prompt with the agent's configuration
       */
      process: async (userPrompt: string, additionalConfig: Partial<AgentConfig> = {}): Promise<AgentResponse> => {
        const startTime = Date.now();
        
        try {
          this.logger.debug(`🤖 ${agentName} agent processing request`);
          
          // Merge configurations
          const finalConfig: AIProviderConfig = {
            maxTokens: additionalConfig.maxTokens || config.maxTokens || 4000,
            temperature: additionalConfig.temperature || config.temperature || 0.1,
            topP: additionalConfig.topP || config.topP || 0.9,
            stopSequences: additionalConfig.stopSequences || config.stopSequences,
            model: additionalConfig.model || config.model,
          };

          // Prepare messages
          const messages: ClaudeMessage[] = [];
          
          // Add system prompt if provided
          if (config.systemPrompt || additionalConfig.systemPrompt) {
            messages.push({
              role: 'system',
              content: additionalConfig.systemPrompt || config.systemPrompt!
            });
          }

          // Add user prompt
          messages.push({
            role: 'user',
            content: userPrompt
          });

          // Send to AI provider
          const response = await this.aiProvider.sendMessage(messages, finalConfig);
          const endTime = Date.now();

          this.logger.debug(`✅ ${agentName} agent completed in ${endTime - startTime}ms using ${response.provider}`);

          return {
            content: response.content,
            usage: response.usage,
            provider: response.provider,
            model: response.model,
            processingTime: endTime - startTime
          };

        } catch (error) {
          const endTime = Date.now();
          this.logger.error(`❌ ${agentName} agent failed after ${endTime - startTime}ms:`, error);
          throw new Error(`${agentName} agent processing failed: ${error.message}`);
        }
      },

      /**
       * Process with conversation history
       */
      processConversation: async (
        messages: ClaudeMessage[], 
        additionalConfig: Partial<AgentConfig> = {}
      ): Promise<AgentResponse> => {
        const startTime = Date.now();
        
        try {
          this.logger.debug(`🤖 ${agentName} agent processing conversation with ${messages.length} messages`);
          
          // Merge configurations
          const finalConfig: AIProviderConfig = {
            maxTokens: additionalConfig.maxTokens || config.maxTokens || 4000,
            temperature: additionalConfig.temperature || config.temperature || 0.1,
            topP: additionalConfig.topP || config.topP || 0.9,
            stopSequences: additionalConfig.stopSequences || config.stopSequences,
            model: additionalConfig.model || config.model,
          };

          // Prepend system prompt if provided and not already in messages
          const finalMessages = [...messages];
          if ((config.systemPrompt || additionalConfig.systemPrompt) && 
              !messages.some(m => m.role === 'system')) {
            finalMessages.unshift({
              role: 'system',
              content: additionalConfig.systemPrompt || config.systemPrompt!
            });
          }

          // Send to AI provider
          const response = await this.aiProvider.sendMessage(finalMessages, finalConfig);
          const endTime = Date.now();

          this.logger.debug(`✅ ${agentName} agent conversation completed in ${endTime - startTime}ms using ${response.provider}`);

          return {
            content: response.content,
            usage: response.usage,
            provider: response.provider,
            model: response.model,
            processingTime: endTime - startTime
          };

        } catch (error) {
          const endTime = Date.now();
          this.logger.error(`❌ ${agentName} agent conversation failed after ${endTime - startTime}ms:`, error);
          throw new Error(`${agentName} agent conversation processing failed: ${error.message}`);
        }
      }
    };
  }

  /**
   * Create pre-configured agents for common tasks
   */
  createFileClassificationAgent() {
    return this.createAgent('FileClassification', {
      systemPrompt: `You are an expert document classifier specializing in expense and financial documents.
      Analyze the provided document content and classify it accurately based on its type, purpose, and structure.

      CRITICAL: You must respond with ONLY valid JSON. No explanations, no markdown, no additional text.
      Your response must be parseable by JSON.parse().`,
      temperature: 0.1,
      maxTokens: 2000
    });
  }

  createDataExtractionAgent() {
    return this.createAgent('DataExtraction', {
      systemPrompt: `You are a precise data extraction specialist. Extract structured information from documents 
      with high accuracy, following the provided schema exactly. Return only valid JSON responses.`,
      temperature: 0.0,
      maxTokens: 4000
    });
  }

  createIssueDetectionAgent() {
    return this.createAgent('IssueDetection', {
      systemPrompt: `You are a compliance and quality assurance expert. Analyze documents for potential issues, 
      compliance violations, and data quality problems. Provide detailed explanations for any issues found.`,
      temperature: 0.1,
      maxTokens: 3000
    });
  }

  createCitationGeneratorAgent() {
    return this.createAgent('CitationGenerator', {
      systemPrompt: `You are a citation and reference specialist. Generate accurate citations and references 
      for extracted data, linking back to specific sections of the source document.`,
      temperature: 0.1,
      maxTokens: 2000
    });
  }

  createImageQualityAgent() {
    return this.createAgent('ImageQuality', {
      systemPrompt: `You are an image quality assessment expert. Analyze document images for clarity, 
      readability, completeness, and any quality issues that might affect data extraction.`,
      temperature: 0.1,
      maxTokens: 1500
    });
  }

  /**
   * Health check for the AI provider system
   */
  async healthCheck() {
    try {
      const health = await this.aiProvider.healthCheck();
      return {
        status: 'healthy',
        ...health,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('❌ AI Provider health check failed:', error);
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get provider configuration
   */
  getConfiguration() {
    return this.aiProvider.getProviderConfig();
  }
}
