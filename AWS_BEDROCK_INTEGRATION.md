# 🚀 AWS Bedrock + Anthropic API Integration

## 📋 **Overview**

This implementation provides **AWS Bedrock Claude integration** with **Anthropic API fallback**, offering a robust, cost-effective AI solution with automatic failover capabilities.

## 🏗️ **Architecture**

### **Three-Layer Design:**
```
┌─────────────────────────────────────────────────┐
│ AI Agent Factory Service                        │ ← High-level agent creation
│ • Pre-configured agents                         │
│ • Task-specific configurations                  │
└─────────────────────────────────────────────────┘
                        │
                        ▼
┌─────────────────────────────────────────────────┐
│ AI Provider Service                             │ ← Provider selection & fallback
│ • Primary/fallback logic                       │
│ • Error handling & routing                     │
└─────────────────────────────────────────────────┘
                        │
                        ▼
┌─────────────────────────────────────────────────┐
│ Bedrock Service    │    Anthropic Service       │ ← Provider implementations
│ • AWS SDK          │    • Direct API            │
│ • Claude models    │    • Existing integration  │
└─────────────────────────────────────────────────┘
```

## ⚙️ **Configuration**

### **Environment Variables:**
```bash
# Primary provider selection
AI_PROVIDER=bedrock  # Options: bedrock, anthropic

# Fallback configuration
AI_FALLBACK_ENABLED=true  # Enable automatic fallback

# AWS Textract Configuration (separate from Bedrock)
TEXTRACT_AWS_ACCESS_KEY_ID=your_textract_access_key
TEXTRACT_AWS_SECRET_ACCESS_KEY=your_textract_secret_key
TEXTRACT_AWS_SESSION_TOKEN=your_textract_session_token  # Optional
TEXTRACT_AWS_REGION=us-east-1

# AWS Bedrock Configuration (separate from Textract)
BEDROCK_AWS_ACCESS_KEY_ID=your_bedrock_access_key
BEDROCK_AWS_SECRET_ACCESS_KEY=your_bedrock_secret_key
BEDROCK_AWS_SESSION_TOKEN=your_bedrock_session_token  # Optional
BEDROCK_AWS_REGION=us-east-1
BEDROCK_CLAUDE_MODEL=anthropic.claude-3-5-sonnet-20241022-v2:0

# Anthropic API settings (fallback)
ANTHROPIC_API_KEY=your_anthropic_key
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022
```

## 🔄 **Provider Selection Logic**

### **Primary Provider:**
- **Default**: AWS Bedrock (cost-effective, AWS integrated)
- **Alternative**: Anthropic API (set `AI_PROVIDER=anthropic`)

### **Fallback Behavior:**
1. **Try Primary Provider** (Bedrock or Anthropic)
2. **On Failure**: Automatically try secondary provider
3. **Error Reporting**: Detailed error information for both attempts
4. **Disable Fallback**: Set `AI_FALLBACK_ENABLED=false`

## 🛠️ **Error Handling**

### **Bedrock-Specific Errors:**
- **BEDROCK_VALIDATION_ERROR**: Invalid request parameters
- **BEDROCK_THROTTLING_ERROR**: Rate limit exceeded
- **BEDROCK_MODEL_NOT_READY**: Model not available
- **BEDROCK_QUOTA_EXCEEDED**: Service quota exceeded
- **BEDROCK_ACCESS_DENIED**: Insufficient permissions
- **BEDROCK_UNKNOWN_ERROR**: Other Bedrock errors

### **Fallback Triggers:**
- **Any Bedrock Error**: Automatically tries Anthropic API
- **Any Anthropic Error**: Automatically tries Bedrock (if Anthropic is primary)
- **Both Fail**: Returns combined error message

## 📊 **Available Models**

### **AWS Bedrock Models:**
- `anthropic.claude-3-5-sonnet-20241022-v2:0` (Default)
- `anthropic.claude-3-sonnet-20240229-v1:0`
- `anthropic.claude-3-haiku-20240307-v1:0`
- `anthropic.claude-v2:1`
- `anthropic.claude-v2:0`

### **Anthropic API Models:**
- `claude-3-5-sonnet-20241022` (Default)
- `claude-3-sonnet-20240229`
- `claude-3-haiku-20240307`

## 🧪 **Testing & Health Checks**

### **Health Check Endpoints:**
```bash
# Overall health status
GET /ai/health

# Provider configuration
GET /ai/config

# Test both providers
GET /ai/test
```

### **Health Check Response:**
```json
{
  "status": "healthy",
  "bedrock": {
    "status": "healthy",
    "model": "anthropic.claude-3-5-sonnet-20241022-v2:0",
    "region": "us-east-1"
  },
  "anthropic": {
    "status": "healthy", 
    "model": "claude-3-5-sonnet-20241022"
  },
  "primary": "bedrock",
  "fallback": true,
  "timestamp": "2024-07-30T19:00:00.000Z"
}
```

## 🎯 **Usage Examples**

### **Basic Agent Usage:**
```typescript
// Create specialized agent
const extractionAgent = aiAgentFactory.createDataExtractionAgent();

// Process document
const result = await extractionAgent.process(documentContent);

console.log(`Response from ${result.provider}: ${result.content}`);
console.log(`Processing time: ${result.processingTime}ms`);
```

### **Custom Agent Configuration:**
```typescript
// Create custom agent
const customAgent = aiAgentFactory.createAgent('CustomTask', {
  systemPrompt: 'You are a specialized assistant...',
  temperature: 0.2,
  maxTokens: 2000
});

// Process with additional config
const result = await customAgent.process(userInput, {
  temperature: 0.1,  // Override default
  model: 'anthropic.claude-3-haiku-20240307-v1:0'  // Use specific model
});
```

### **Conversation Processing:**
```typescript
const messages = [
  { role: 'system', content: 'You are a helpful assistant.' },
  { role: 'user', content: 'Hello!' },
  { role: 'assistant', content: 'Hi there!' },
  { role: 'user', content: 'How are you?' }
];

const result = await agent.processConversation(messages);
```

## 💰 **Cost Optimization**

### **Bedrock Advantages:**
- **Lower Costs**: Generally cheaper than Anthropic API
- **AWS Integration**: Unified billing and management
- **Regional Availability**: Reduced latency in AWS regions

### **Fallback Benefits:**
- **High Availability**: Automatic failover ensures service continuity
- **Provider Diversity**: Reduces dependency on single provider
- **Performance Optimization**: Can route to fastest available provider

## 🔧 **Integration Points**

### **Existing Agent Integration:**
The new system is designed to be **drop-in compatible** with existing agents:

1. **File Classification Agent**: Uses `createFileClassificationAgent()`
2. **Data Extraction Agent**: Uses `createDataExtractionAgent()`
3. **Issue Detection Agent**: Uses `createIssueDetectionAgent()`
4. **Citation Generator Agent**: Uses `createCitationGeneratorAgent()`
5. **Image Quality Agent**: Uses `createImageQualityAgent()`

### **Migration Path:**
1. **Phase 1**: Deploy with `AI_PROVIDER=anthropic` (no change)
2. **Phase 2**: Switch to `AI_PROVIDER=bedrock` (cost savings)
3. **Phase 3**: Enable fallback for maximum reliability

## 📈 **Monitoring & Logging**

### **Detailed Logging:**
```
🤖 AI Provider initialized - Primary: bedrock, Fallback: enabled
🏗️ Bedrock Claude service initialized with model: anthropic.claude-3-5-sonnet-20241022-v2:0
🌍 Using AWS region: us-east-1
🤖 DataExtraction agent processing request
✅ DataExtraction agent completed in 1250ms using bedrock
```

### **Error Logging:**
```
❌ Primary provider (bedrock) failed: BEDROCK_THROTTLING_ERROR: Rate limit exceeded
🔄 Attempting fallback to anthropic...
✅ DataExtraction agent completed in 1800ms using anthropic
```

### **Performance Metrics:**
- **Processing Time**: Individual request timing
- **Provider Usage**: Track Bedrock vs Anthropic usage
- **Error Rates**: Monitor failure rates by provider
- **Cost Tracking**: Usage metrics for cost analysis

## 🚀 **Benefits Summary**

### **✅ Cost Efficiency:**
- **Primary Bedrock**: Lower costs for most requests
- **Smart Fallback**: Maintains service quality
- **Usage Optimization**: Automatic provider selection

### **✅ Reliability:**
- **Dual Provider**: Eliminates single point of failure
- **Automatic Failover**: Seamless error recovery
- **Health Monitoring**: Proactive issue detection

### **✅ Performance:**
- **Regional Optimization**: Bedrock in same AWS region
- **Parallel Capability**: Maintains existing parallel processing
- **Response Tracking**: Detailed performance metrics

### **✅ Maintainability:**
- **Drop-in Replacement**: Compatible with existing code
- **Environment Configuration**: Easy provider switching
- **Comprehensive Logging**: Detailed debugging information

The AWS Bedrock integration provides a robust, cost-effective AI solution while maintaining full compatibility with the existing parallel processing system and providing automatic fallback for maximum reliability.
