import { Controller, Get } from '@nestjs/common';
import { AIAgentFactoryService } from '../services/ai-agent-factory.service';

@Controller('ai')
export class AIHealthController {
  constructor(private readonly aiAgentFactory: AIAgentFactoryService) {}

  @Get('health')
  async getHealth() {
    return await this.aiAgentFactory.healthCheck();
  }

  @Get('config')
  async getConfig() {
    return this.aiAgentFactory.getConfiguration();
  }

  @Get('test')
  async testProviders() {
    try {
      const testAgent = this.aiAgentFactory.createAgent('Test', {
        systemPrompt: 'You are a test agent. Respond with a brief confirmation.',
        maxTokens: 50
      });

      const response = await testAgent.process('Hello, please confirm you are working.');
      
      return {
        status: 'success',
        response: response.content,
        provider: response.provider,
        model: response.model,
        processingTime: response.processingTime,
        usage: response.usage
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message
      };
    }
  }
}
