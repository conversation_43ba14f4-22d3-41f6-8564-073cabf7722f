import { Injectable, Logger } from '@nestjs/common';
import { BedrockRuntimeClient, InvokeModelCommand } from '@aws-sdk/client-bedrock-runtime';

export interface BedrockClaudeConfig {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stopSequences?: string[];
}

export interface ClaudeMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ClaudeResponse {
  content: Array<{
    type: 'text';
    text: string;
  }>;
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
  stop_reason: string;
}

@Injectable()
export class BedrockClaudeService {
  private readonly logger = new Logger(BedrockClaudeService.name);
  private bedrockClient: BedrockRuntimeClient;
  private defaultModel: string;

  constructor() {
    // Use Bedrock-specific AWS configuration
    const awsRegion = process.env.BEDROCK_AWS_REGION || 'us-east-1';

    // Validate required Bedrock credentials
    if (!process.env.BEDROCK_AWS_ACCESS_KEY_ID || !process.env.BEDROCK_AWS_SECRET_ACCESS_KEY) {
      throw new Error('Bedrock AWS credentials not configured. Please set BEDROCK_AWS_ACCESS_KEY_ID and BEDROCK_AWS_SECRET_ACCESS_KEY');
    }

    // Initialize Bedrock client with Bedrock-specific credentials
    this.bedrockClient = new BedrockRuntimeClient({
      region: awsRegion,
      credentials: {
        accessKeyId: process.env.BEDROCK_AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.BEDROCK_AWS_SECRET_ACCESS_KEY,
        sessionToken: process.env.BEDROCK_AWS_SESSION_TOKEN, // Optional
      },
    });

    // Default to Claude 3.7 Sonnet on Bedrock
    this.defaultModel = process.env.BEDROCK_CLAUDE_MODEL || 'anthropic.claude-3-7-sonnet-20250219-v1:0';

    this.logger.log(`🏗️ Bedrock Claude service initialized with model: ${this.defaultModel}`);
    this.logger.log(`🌍 Using Bedrock AWS region: ${awsRegion}`);
    this.logger.log(`🔑 Using Bedrock-specific AWS credentials`);
  }

  /**
   * Send a message to Claude via AWS Bedrock
   */
  async sendMessage(
    messages: ClaudeMessage[],
    config: BedrockClaudeConfig = {}
  ): Promise<ClaudeResponse> {
    try {
      const modelId = config.model || this.defaultModel;
      
      this.logger.debug(`🤖 Sending request to Bedrock Claude model: ${modelId}`);
      this.logger.debug(`📝 Message count: ${messages.length}`);

      // Prepare the request payload for Bedrock Claude
      const requestBody = {
        anthropic_version: "bedrock-2023-05-31",
        max_tokens: config.maxTokens || 4000,
        temperature: config.temperature || 0.1,
        top_p: config.topP || 0.9,
        messages: messages.map(msg => ({
          role: msg.role === 'system' ? 'user' : msg.role, // Bedrock doesn't support system role directly
          content: msg.content
        })),
        ...(config.stopSequences && { stop_sequences: config.stopSequences })
      };

      // Handle system messages by prepending to first user message
      const systemMessages = messages.filter(m => m.role === 'system');
      if (systemMessages.length > 0) {
        const systemPrompt = systemMessages.map(m => m.content).join('\n\n');
        const userMessages = messages.filter(m => m.role !== 'system');
        
        if (userMessages.length > 0) {
          requestBody.messages = [
            {
              role: 'user',
              content: `${systemPrompt}\n\n${userMessages[0].content}`
            },
            ...userMessages.slice(1).map(msg => ({
              role: msg.role as 'user' | 'assistant', // Filter out system role
              content: msg.content
            }))
          ];
        }
      }

      const command = new InvokeModelCommand({
        modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody),
      });

      const startTime = Date.now();
      const response = await this.bedrockClient.send(command);
      const endTime = Date.now();

      if (!response.body) {
        throw new Error('No response body received from Bedrock');
      }

      // Parse the response
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      
      this.logger.debug(`✅ Bedrock response received in ${endTime - startTime}ms`);
      this.logger.debug(`📊 Tokens - Input: ${responseBody.usage?.input_tokens || 'N/A'}, Output: ${responseBody.usage?.output_tokens || 'N/A'}`);

      return responseBody as ClaudeResponse;

    } catch (error) {
      this.logger.error(`❌ Bedrock Claude request failed:`);
      this.logger.error(`🔍 Error Name: ${error.name}`);
      this.logger.error(`🔍 Error Message: ${error.message}`);
      this.logger.error(`🔍 Model Used: ${config.model || this.defaultModel}`);
      this.logger.error(`🔍 Region: ${process.env.BEDROCK_AWS_REGION || 'us-east-1'}`);
      this.logger.error(`🔍 Full Error Object:`, JSON.stringify(error, null, 2));
      this.logger.error(`🔍 Error Stack:`, error.stack);

      // Enhance error information for better debugging
      const enhancedError = new Error(`Bedrock Claude Error: ${error.message}`);
      (enhancedError as any).originalError = error;
      (enhancedError as any).provider = 'bedrock';
      (enhancedError as any).model = config.model || this.defaultModel;
      
      // Add specific error codes for different failure types
      if (error.name === 'ValidationException') {
        (enhancedError as any).errorCode = 'BEDROCK_VALIDATION_ERROR';
      } else if (error.name === 'ThrottlingException') {
        (enhancedError as any).errorCode = 'BEDROCK_THROTTLING_ERROR';
      } else if (error.name === 'ModelNotReadyException') {
        (enhancedError as any).errorCode = 'BEDROCK_MODEL_NOT_READY';
      } else if (error.name === 'ServiceQuotaExceededException') {
        (enhancedError as any).errorCode = 'BEDROCK_QUOTA_EXCEEDED';
      } else if (error.name === 'AccessDeniedException') {
        (enhancedError as any).errorCode = 'BEDROCK_ACCESS_DENIED';
      } else {
        (enhancedError as any).errorCode = 'BEDROCK_UNKNOWN_ERROR';
      }

      throw enhancedError;
    }
  }

  /**
   * Simple text completion method for backward compatibility
   */
  async complete(
    prompt: string,
    config: BedrockClaudeConfig = {}
  ): Promise<string> {
    const messages: ClaudeMessage[] = [
      { role: 'user', content: prompt }
    ];

    const response = await this.sendMessage(messages, config);
    
    if (response.content && response.content.length > 0) {
      return response.content[0].text;
    }
    
    throw new Error('No text content in Bedrock Claude response');
  }

  /**
   * Health check method to verify Bedrock connectivity
   */
  async healthCheck(): Promise<{ status: string; model: string; region: string }> {
    try {
      await this.complete('Hello', { maxTokens: 10 });
      return {
        status: 'healthy',
        model: this.defaultModel,
        region: process.env.BEDROCK_AWS_REGION || 'us-east-1'
      };
    } catch (error) {
      this.logger.warn(`🔍 Bedrock health check failed: ${error.message}`);
      return {
        status: 'unhealthy',
        model: this.defaultModel,
        region: process.env.BEDROCK_AWS_REGION || 'us-east-1'
      };
    }
  }

  /**
   * Get available models (placeholder for future implementation)
   */
  getAvailableModels(): string[] {
    return [
      'anthropic.claude-3-7-sonnet-20250219-v1:0',
      'anthropic.claude-3-5-sonnet-20241022-v2:0',
      'anthropic.claude-3-sonnet-20240229-v1:0',
      'anthropic.claude-3-haiku-20240307-v1:0',
      'anthropic.claude-v2:1',
      'anthropic.claude-v2:0'
    ];
  }
}
