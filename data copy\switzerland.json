{"switzerlandExpenseReimbursementDatabaseTables": {"fileRelatedRequirements": [{"fieldType": "Customer Name on Invoice", "description": "Local Employer name as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Must show Global PPL CH GmbH as customer"}, {"fieldType": "Customer Address on Invoice", "description": "Local Employer address as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Must show Freigutstrasse 2 8002 Zürich, Switzerland"}, {"fieldType": "Customer Registration on Invoice", "description": "Local Employer registration as customer on supplier invoice", "receiptType": "All", "icpSpecific": true, "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Must show CHE-295.369.918"}, {"fieldType": "Customer Name Exception", "description": "Exception for flights/bookings where Local Employer name not possible", "receiptType": "Travel", "icpSpecific": true, "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Optional", "rule": "Worker's name acceptable when Local Employer name not possible"}, {"fieldType": "<PERSON><PERSON><PERSON><PERSON>", "description": "Receipt currency and exchange rate", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Local currency with FX rate calculation"}, {"fieldType": "Amount", "description": "Expense amount", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must be clearly stated on receipt"}, {"fieldType": "Receipt Type", "description": "Type of supporting document", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must be actual tax receipts or invoices, not booking confirmations"}, {"fieldType": "Personal Information", "description": "Privacy requirement for receipts", "receiptType": "All", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Any personal information not required for reimbursement must be removed"}, {"fieldType": "Business Trip Reporting", "description": "Separate reports requirement", "receiptType": "Travel", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Submit separate report per each business trip"}, {"fieldType": "Travel Template", "description": "Specific reporting template", "receiptType": "Travel", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Must use Travel Expense Report Template Switzerland CHF.xlsx"}, {"fieldType": "Manager <PERSON><PERSON><PERSON><PERSON>", "description": "Direct manager approval", "receiptType": "Training", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Required for training expenses"}, {"fieldType": "Route Map", "description": "Travel route documentation", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Map with relevant route (Google Maps sufficient)"}, {"fieldType": "Car Details", "description": "Vehicle information", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Car details and destination required"}, {"fieldType": "Logbook", "description": "Vehicle usage documentation", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Logbook required for each used car"}, {"fieldType": "Combined Mileage", "description": "Multiple vehicle tracking", "receiptType": "Mileage", "icpSpecific": false, "icpName": "All ICPs", "mandatoryOptional": "Mandatory", "rule": "Combined mileage total if using more than one vehicle per year"}], "complianceAndPolicies": [{"type": "Business Expenses (Non-Travel)", "description": "General business expenses for job completion", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Usually tax exempt, grossed up if not tax free", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide sufficient proof like tax receipts or invoices"}, {"type": "Office Equipment", "description": "Laptops, office supplies, etc.", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Tax-free with sufficient proof", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide sufficient proof (receipts, invoices) and ensure Local Employer name on invoice"}, {"type": "Small Business Expenses", "description": "Small business expenses during trips", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "CHF 20 maximum", "grossUpRule": "Tax-free up to CHF 20", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - no additional information needed"}, {"type": "Training", "description": "Training expenses", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Tax exempt with manager approval", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must get approval from your direct manager"}, {"type": "Mileage", "description": "Private vehicles, cars, vans, motorbikes, bicycles", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Official rates apply", "grossUpRule": "Based on official Swiss mileage rates", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt is not applicable - you must provide map with route (Google Maps sufficient), car details, destination, and logbook for each car used"}, {"type": "Combined Vehicle Mileage", "description": "Multiple vehicles used in one year", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Combined", "grossUpRule": "All mileage calculated based on combined total", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt is not applicable - you must track combined mileage total for all vehicles used"}, {"type": "Domestic Business Travel", "description": "Domestic business trips", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Set per diem rates", "grossUpRule": "Breakfast CHF 15, Lunch CHF 35, Dinner CHF 40", "additionalInfoRequired": false, "additionalInfoDescription": "Receipt is enough - per diem covers meals, additional costs reimbursed against receipts"}, {"type": "Domestic Travel Alternative", "description": "Alternative to per diem for domestic travel", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "No limit specified", "grossUpRule": "Can reimburse meals against receipts instead of per diem", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide receipts for meals if not using per diem method"}, {"type": "International Business Travel", "description": "International business trips", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Set per diem rates", "grossUpRule": "Breakfast CHF 15, Lunch CHF 35, Dinner CHF 40", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - overnight expenses (hotels, transport) must be reimbursed against receipts"}, {"type": "International Travel Enhanced", "description": "International travel with increased per diem", "icpSpecific": false, "icpName": "All ICPs", "grossUpLimit": "Excess amount taxable", "grossUpRule": "Portion exceeding set rate is taxable", "additionalInfoRequired": true, "additionalInfoDescription": "Receipt alone is not enough - you must provide receipts for any amount above the set per diem rate"}]}}