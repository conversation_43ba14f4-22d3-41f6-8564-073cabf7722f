# 🔧 AWS Configuration Separation Guide

## 📋 **Overview**

AWS Textract and AWS Bedrock now use **separate AWS configurations**, allowing for different credentials, regions, and permissions for each service.

## 🎯 **Why Separate Configurations?**

### **Different Requirements:**
- **Textract**: Document processing, S3 access, specific regional availability
- **Bedrock**: AI model access, different pricing, different regional availability

### **Security Benefits:**
- **Principle of Least Privilege**: Each service gets only required permissions
- **Credential Isolation**: Textract and Bedrock can use different AWS accounts/roles
- **Risk Reduction**: Compromise of one service doesn't affect the other

### **Operational Flexibility:**
- **Different Regions**: Textract in `us-east-1`, Bedrock in `us-west-2`
- **Different Accounts**: Corporate account for Textract, AI account for Bedrock
- **Different Session Tokens**: Separate temporary credentials

## ⚙️ **Configuration Structure**

### **Textract Configuration:**
```bash
# AWS Textract (Document Processing)
TEXTRACT_AWS_ACCESS_KEY_ID=AKIA...textract
TEXTRACT_AWS_SECRET_ACCESS_KEY=secret...textract
TEXTRACT_AWS_SESSION_TOKEN=token...textract  # Optional
TEXTRACT_AWS_REGION=us-east-1

# Textract-specific settings
TEXTRACT_S3_BUCKET=your-textract-temp-bucket
```

### **Bedrock Configuration:**
```bash
# AWS Bedrock (AI Models)
BEDROCK_AWS_ACCESS_KEY_ID=AKIA...bedrock
BEDROCK_AWS_SECRET_ACCESS_KEY=secret...bedrock
BEDROCK_AWS_SESSION_TOKEN=token...bedrock  # Optional
BEDROCK_AWS_REGION=us-east-1

# Bedrock-specific settings
BEDROCK_CLAUDE_MODEL=anthropic.claude-3-5-sonnet-********-v2:0
```

## 🔄 **Migration from Shared Config**

### **Before (Shared):**
```bash
# Old shared configuration
AWS_ACCESS_KEY_ID=shared_key
AWS_SECRET_ACCESS_KEY=shared_secret
AWS_REGION=us-east-1
```

### **After (Separated):**
```bash
# Textract-specific
TEXTRACT_AWS_ACCESS_KEY_ID=textract_key
TEXTRACT_AWS_SECRET_ACCESS_KEY=textract_secret
TEXTRACT_AWS_REGION=us-east-1

# Bedrock-specific  
BEDROCK_AWS_ACCESS_KEY_ID=bedrock_key
BEDROCK_AWS_SECRET_ACCESS_KEY=bedrock_secret
BEDROCK_AWS_REGION=us-east-1
```

## 🛠️ **Implementation Details**

### **Textract Service Updates:**
- **Credential Priority**: Passed params → Textract env vars → Default credential chain
- **Region Selection**: `TEXTRACT_AWS_REGION` or fallback to `us-east-1`
- **Logging**: Shows when Textract-specific credentials are used

### **Bedrock Service Updates:**
- **Required Validation**: Throws error if Bedrock credentials missing
- **Credential Isolation**: Only uses Bedrock-specific environment variables
- **Enhanced Logging**: Shows Bedrock region and credential source

## 🔍 **Credential Validation**

### **Textract Validation:**
```typescript
// Textract falls back gracefully
if (process.env.TEXTRACT_AWS_ACCESS_KEY_ID && process.env.TEXTRACT_AWS_SECRET_ACCESS_KEY) {
  // Use Textract-specific credentials
} else {
  // Use default AWS credential chain
}
```

### **Bedrock Validation:**
```typescript
// Bedrock requires explicit configuration
if (!process.env.BEDROCK_AWS_ACCESS_KEY_ID || !process.env.BEDROCK_AWS_SECRET_ACCESS_KEY) {
  throw new Error('Bedrock AWS credentials not configured');
}
```

## 📊 **Configuration Examples**

### **Same Account, Different Regions:**
```bash
# Textract in us-east-1 (better for document processing)
TEXTRACT_AWS_ACCESS_KEY_ID=AKIA...shared
TEXTRACT_AWS_SECRET_ACCESS_KEY=secret...shared
TEXTRACT_AWS_REGION=us-east-1

# Bedrock in us-west-2 (better model availability)
BEDROCK_AWS_ACCESS_KEY_ID=AKIA...shared
BEDROCK_AWS_SECRET_ACCESS_KEY=secret...shared
BEDROCK_AWS_REGION=us-west-2
```

### **Different Accounts:**
```bash
# Corporate account for Textract
TEXTRACT_AWS_ACCESS_KEY_ID=AKIA...corporate
TEXTRACT_AWS_SECRET_ACCESS_KEY=secret...corporate
TEXTRACT_AWS_REGION=us-east-1

# AI/ML account for Bedrock
BEDROCK_AWS_ACCESS_KEY_ID=AKIA...ai_account
BEDROCK_AWS_SECRET_ACCESS_KEY=secret...ai_account
BEDROCK_AWS_REGION=us-east-1
```

### **Temporary Credentials (STS):**
```bash
# Textract with temporary credentials
TEXTRACT_AWS_ACCESS_KEY_ID=ASIA...temp
TEXTRACT_AWS_SECRET_ACCESS_KEY=secret...temp
TEXTRACT_AWS_SESSION_TOKEN=IQoJb3JpZ2luX2VjE...
TEXTRACT_AWS_REGION=us-east-1

# Bedrock with different temporary credentials
BEDROCK_AWS_ACCESS_KEY_ID=ASIA...temp2
BEDROCK_AWS_SECRET_ACCESS_KEY=secret...temp2
BEDROCK_AWS_SESSION_TOKEN=IQoJb3JpZ2luX2VjF...
BEDROCK_AWS_REGION=us-east-1
```

## 🔐 **Security Best Practices**

### **IAM Permissions:**

**Textract Role:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "textract:DetectDocumentText",
        "textract:AnalyzeDocument",
        "s3:GetObject",
        "s3:PutObject"
      ],
      "Resource": "*"
    }
  ]
}
```

**Bedrock Role:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "bedrock:InvokeModel",
        "bedrock:ListFoundationModels"
      ],
      "Resource": "*"
    }
  ]
}
```

### **Credential Management:**
- **Rotate Regularly**: Different rotation schedules for each service
- **Monitor Usage**: Separate CloudTrail logs for each service
- **Least Privilege**: Each service gets only required permissions

## 🧪 **Testing Configuration**

### **Textract Test:**
```bash
# Test Textract connectivity
curl -X POST http://localhost:3000/process \
  -H "Content-Type: application/json" \
  -d '{"documentReader": "textract", "file": "test.pdf"}'
```

### **Bedrock Test:**
```bash
# Test Bedrock connectivity
curl -X GET http://localhost:3000/ai/test
```

### **Health Check:**
```bash
# Check both services
curl -X GET http://localhost:3000/ai/health
```

## ✅ **Benefits of Separation**

### **Security:**
- **Isolated Credentials**: Breach of one doesn't affect the other
- **Granular Permissions**: Each service gets exactly what it needs
- **Audit Trail**: Clear separation in logs and monitoring

### **Operational:**
- **Independent Scaling**: Scale Textract and Bedrock independently
- **Regional Optimization**: Use best regions for each service
- **Cost Management**: Separate billing and cost tracking

### **Flexibility:**
- **Different Providers**: Could use different AWS accounts
- **Service Migration**: Easy to migrate one service without affecting the other
- **Environment Separation**: Dev/staging/prod can have different configurations

The separated configuration provides better security, flexibility, and operational control while maintaining full compatibility with existing functionality.
